import {
  QuestionnaireSession,
  QuestionnaireResponse,
  AnalyticsEvent,
} from '../types';
import { logger } from '../utils/logger';
import { generateUUID } from '../utils/uuid';
import AsyncStorage from '@react-native-async-storage/async-storage';

/**
 * Analytics service for tracking questionnaire engagement and user behavior
 * Provides insights for improving the questionnaire experience
 */
export class QuestionnaireAnalytics {
  private static instance: QuestionnaireAnalytics;
  private eventQueue: AnalyticsEvent[] = [];

  private constructor() {
    this.initializeAnalytics();
  }

  public static getInstance(): QuestionnaireAnalytics {
    if (!QuestionnaireAnalytics.instance) {
      QuestionnaireAnalytics.instance = new QuestionnaireAnalytics();
    }
    return QuestionnaireAnalytics.instance;
  }

  /**
   * Initialize analytics system
   */
  private async initializeAnalytics(): Promise<void> {
    try {
      // Load pending events from storage
      const storedEvents = await AsyncStorage.getItem('questionnaire_analytics_queue');
      if (storedEvents) {
        this.eventQueue = JSON.parse(storedEvents);
      }

      logger.info('Questionnaire analytics initialized', {
        pendingEvents: this.eventQueue.length,
      }, 'QuestionnaireAnalytics');

    } catch (error) {
      logger.error('Failed to initialize analytics', error, 'QuestionnaireAnalytics');
    }
  }

  /**
   * Track questionnaire session start
   */
  async trackSessionStart(session: QuestionnaireSession): Promise<void> {
    const event: AnalyticsEvent = {
      id: generateUUID(),
      type: 'questionnaire_session_start',
      userId: session.userId,
      timestamp: new Date(),
      properties: {
        sessionId: session.id,
        sessionType: session.type,
        totalQuestions: session.totalQuestions,
        estimatedTime: session.estimatedTimeRemaining,
      },
    };

    await this.trackEvent(event);
  }

  /**
   * Track questionnaire response
   */
  async trackResponse(response: QuestionnaireResponse): Promise<void> {
    const event: AnalyticsEvent = {
      id: `response_${Date.now()}`,
      type: 'questionnaire_response',
      userId: response.userId,
      timestamp: new Date(),
      properties: {
        sessionId: response.sessionId,
        questionId: response.questionId,
        questionCategory: response.questionCategory,
        responseTime: response.responseTimeMs,
        confidenceLevel: response.confidenceLevel,
        answerType: typeof response.answerValue,
      },
    };

    await this.trackEvent(event);
  }

  /**
   * Track questionnaire session completion
   */
  async trackSessionComplete(session: QuestionnaireSession): Promise<void> {
    const duration = session.completedAt && session.startedAt 
      ? session.completedAt.getTime() - session.startedAt.getTime()
      : 0;

    const event: AnalyticsEvent = {
      id: generateUUID(),
      type: 'questionnaire_session_complete',
      userId: session.userId,
      timestamp: new Date(),
      properties: {
        sessionId: session.id,
        sessionType: session.type,
        totalQuestions: session.totalQuestions,
        answeredQuestions: session.responses.length,
        completionPercentage: session.completionPercentage,
        durationMs: duration,
        insightCount: session.insights?.length || 0,
        recommendationCount: session.recommendations?.length || 0,
      },
    };

    await this.trackEvent(event);
  }

  /**
   * Track questionnaire abandonment
   */
  async trackSessionAbandoned(session: QuestionnaireSession, reason: string): Promise<void> {
    const event: AnalyticsEvent = {
      id: generateUUID(),
      type: 'questionnaire_session_abandoned',
      userId: session.userId,
      timestamp: new Date(),
      properties: {
        sessionId: session.id,
        sessionType: session.type,
        completionPercentage: session.completionPercentage,
        answeredQuestions: session.responses.length,
        totalQuestions: session.totalQuestions,
        abandonmentReason: reason,
        timeSpent: Date.now() - session.startedAt.getTime(),
      },
    };

    await this.trackEvent(event);
  }

  /**
   * Track question skip
   */
  async trackQuestionSkip(
    sessionId: string,
    userId: string,
    questionId: string,
    reason: string
  ): Promise<void> {
    const event: AnalyticsEvent = {
      id: `question_skip_${Date.now()}`,
      type: 'questionnaire_question_skip',
      userId,
      timestamp: new Date(),
      properties: {
        sessionId,
        questionId,
        skipReason: reason,
      },
    };

    await this.trackEvent(event);
  }

  /**
   * Track user engagement metrics
   */
  async trackEngagement(
    userId: string,
    engagementType: 'question_focus' | 'question_blur' | 'help_viewed' | 'back_navigation',
    properties: Record<string, any> = {}
  ): Promise<void> {
    const event: AnalyticsEvent = {
      id: `engagement_${Date.now()}`,
      type: 'questionnaire_engagement',
      userId,
      timestamp: new Date(),
      properties: {
        engagementType,
        ...properties,
      },
    };

    await this.trackEvent(event);
  }

  /**
   * Track recommendation interaction
   */
  async trackRecommendationInteraction(
    userId: string,
    recommendationId: string,
    interactionType: 'viewed' | 'started' | 'completed' | 'dismissed',
    properties: Record<string, any> = {}
  ): Promise<void> {
    const event: AnalyticsEvent = {
      id: `recommendation_${Date.now()}`,
      type: 'recommendation_interaction',
      userId,
      timestamp: new Date(),
      properties: {
        recommendationId,
        interactionType,
        ...properties,
      },
    };

    await this.trackEvent(event);
  }

  /**
   * Generic event tracking
   */
  private async trackEvent(event: AnalyticsEvent): Promise<void> {
    try {
      // Add to queue
      this.eventQueue.push(event);

      // Save queue to storage
      await AsyncStorage.setItem('questionnaire_analytics_queue', JSON.stringify(this.eventQueue));

      // Try to send immediately if online
      const netInfo = await import('@react-native-community/netinfo').then(m => m.default.fetch());
      if (netInfo.isConnected && netInfo.isInternetReachable) {
        await this.flushEvents();
      }

      logger.debug('Analytics event tracked', {
        eventType: event.type,
        userId: event.userId,
        queueSize: this.eventQueue.length,
      }, 'QuestionnaireAnalytics');

    } catch (error) {
      logger.error('Failed to track analytics event', error, 'QuestionnaireAnalytics');
    }
  }

  /**
   * Flush events to analytics service
   */
  private async flushEvents(): Promise<void> {
    if (this.eventQueue.length === 0) return;

    try {
      // In a real implementation, you would send these to your analytics service
      // For now, we'll just log them and clear the queue
      
      const eventsToSend = [...this.eventQueue];
      
      // TODO: Send to analytics service (e.g., Firebase Analytics, Mixpanel, etc.)
      // await analyticsService.sendEvents(eventsToSend);

      // Clear the queue after successful send
      this.eventQueue = [];
      await AsyncStorage.removeItem('questionnaire_analytics_queue');

      logger.info('Analytics events flushed', {
        eventCount: eventsToSend.length,
      }, 'QuestionnaireAnalytics');

    } catch (error) {
      logger.error('Failed to flush analytics events', error, 'QuestionnaireAnalytics');
    }
  }

  /**
   * Get questionnaire completion metrics
   */
  async getCompletionMetrics(userId: string): Promise<{
    totalSessions: number;
    completedSessions: number;
    completionRate: number;
    averageCompletionTime: number;
    mostSkippedQuestions: string[];
  }> {
    try {
      // This would typically query your analytics database
      // For now, return mock data based on local storage
      
      const sessions = await this.getSessionHistory(userId);
      const completedSessions = sessions.filter(s => s.completionPercentage === 100);
      
      const completionRate = sessions.length > 0 ? (completedSessions.length / sessions.length) * 100 : 0;
      
      const averageCompletionTime = completedSessions.length > 0
        ? completedSessions.reduce((sum, session) => {
            const duration = session.completedAt && session.startedAt
              ? session.completedAt.getTime() - session.startedAt.getTime()
              : 0;
            return sum + duration;
          }, 0) / completedSessions.length
        : 0;

      return {
        totalSessions: sessions.length,
        completedSessions: completedSessions.length,
        completionRate,
        averageCompletionTime,
        mostSkippedQuestions: [], // Would be calculated from response data
      };

    } catch (error) {
      logger.error('Failed to get completion metrics', error, 'QuestionnaireAnalytics');
      return {
        totalSessions: 0,
        completedSessions: 0,
        completionRate: 0,
        averageCompletionTime: 0,
        mostSkippedQuestions: [],
      };
    }
  }

  /**
   * Get session history for analytics
   */
  private async getSessionHistory(userId: string): Promise<QuestionnaireSession[]> {
    try {
      // This would typically query your database
      // For now, get from offline storage
      const offlineManager = await import('./offlineQuestionnaireManager').then(m => m.offlineQuestionnaireManager);
      return await offlineManager.getOfflineSessionsForUser(userId);
    } catch (error) {
      logger.error('Failed to get session history', error, 'QuestionnaireAnalytics');
      return [];
    }
  }

  /**
   * Force flush all pending events
   */
  async forcFlush(): Promise<void> {
    await this.flushEvents();
  }
}

export const questionnaireAnalytics = QuestionnaireAnalytics.getInstance();
